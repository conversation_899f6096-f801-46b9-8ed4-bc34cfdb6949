package com.sdesrd.filetransfer.server.util;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件权限清理测试
 * 验证测试结束后能够正确清理只读文件和目录
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@DisplayName("文件权限清理测试")
class FilePermissionCleanupTest {
    
    @TempDir
    Path tempDir;
    
    private File testFile;
    private File testDir;
    private File subFile;
    
    @BeforeEach
    void setUp() throws IOException {
        // 创建测试文件
        testFile = tempDir.resolve("test-file.txt").toFile();
        Files.write(testFile.toPath(), "测试文件内容".getBytes());
        
        // 创建测试目录
        testDir = tempDir.resolve("test-dir").toFile();
        testDir.mkdirs();
        
        // 在测试目录中创建子文件
        subFile = new File(testDir, "sub-file.txt");
        Files.write(subFile.toPath(), "子文件内容".getBytes());
        
        log.info("测试环境初始化完成 - 临时目录: {}", tempDir);
    }
    
    @AfterEach
    void tearDown() {
        // 使用增强的清理方法，确保所有文件都能被删除
        try {
            // 恢复所有文件和目录的可写权限
            if (testFile != null && testFile.exists()) {
                FilePermissionUtils.setWritable(testFile);
            }
            if (testDir != null && testDir.exists()) {
                FilePermissionUtils.setDirectoryPermissions(testDir, false);
            }
            
            log.info("测试清理完成 - 权限已恢复");
        } catch (Exception e) {
            log.error("测试清理时发生异常", e);
        }
    }
    
    @Test
    @DisplayName("测试只读文件清理")
    void testReadOnlyFileCleanup() throws IOException {
        // 设置文件为只读
        boolean setResult = FilePermissionUtils.setReadOnly(testFile);
        assertTrue(setResult, "设置只读权限应成功");
        assertTrue(FilePermissionUtils.isReadOnly(testFile), "文件应为只读");
        
        // 模拟测试结束时的清理操作
        FilePermissionUtils.setWritable(testFile);
        assertTrue(testFile.canWrite(), "文件应恢复为可写");
        
        // 验证文件可以被删除
        boolean deleted = testFile.delete();
        assertTrue(deleted, "只读文件在恢复权限后应能被删除");
        assertFalse(testFile.exists(), "文件应已被删除");
    }
    
    @Test
    @DisplayName("测试只读目录清理")
    void testReadOnlyDirectoryCleanup() throws IOException {
        // 设置目录及其子文件为只读
        boolean setResult = FilePermissionUtils.setDirectoryPermissions(testDir, true);
        assertTrue(setResult, "设置目录只读权限应成功");
        assertFalse(testDir.canWrite(), "目录应为只读");
        assertFalse(subFile.canWrite(), "子文件应为只读");
        
        // 模拟测试结束时的清理操作
        FilePermissionUtils.setDirectoryPermissions(testDir, false);
        assertTrue(testDir.canWrite(), "目录应恢复为可写");
        assertTrue(subFile.canWrite(), "子文件应恢复为可写");
        
        // 验证文件和目录可以被删除
        boolean subFileDeleted = subFile.delete();
        assertTrue(subFileDeleted, "子文件在恢复权限后应能被删除");
        
        boolean dirDeleted = testDir.delete();
        assertTrue(dirDeleted, "目录在恢复权限后应能被删除");
        
        assertFalse(subFile.exists(), "子文件应已被删除");
        assertFalse(testDir.exists(), "目录应已被删除");
    }
    
    @Test
    @DisplayName("测试FileTransferTestUtils递归删除")
    void testFileTransferTestUtilsRecursiveDelete() throws IOException {
        // 创建嵌套目录结构
        File nestedDir = new File(testDir, "nested");
        nestedDir.mkdirs();
        
        File nestedFile = new File(nestedDir, "nested-file.txt");
        Files.write(nestedFile.toPath(), "嵌套文件内容".getBytes());
        
        // 设置所有文件和目录为只读
        FilePermissionUtils.setDirectoryPermissions(testDir, true);
        
        // 验证文件确实为只读
        assertFalse(testDir.canWrite(), "根目录应为只读");
        assertFalse(nestedDir.canWrite(), "嵌套目录应为只读");
        assertFalse(subFile.canWrite(), "子文件应为只读");
        assertFalse(nestedFile.canWrite(), "嵌套文件应为只读");
        
        // 使用FileTransferTestUtils递归删除
        boolean deleted = FileTransferTestUtils.deleteDirectoryRecursively(testDir);
        assertTrue(deleted, "递归删除应成功");
        assertFalse(testDir.exists(), "目录应已被删除");
    }
    
    @Test
    @DisplayName("测试FileTransferTestUtils清理目录")
    void testFileTransferTestUtilsCleanupDirectory() throws IOException {
        // 设置子文件为只读
        FilePermissionUtils.setReadOnly(subFile);
        assertFalse(subFile.canWrite(), "子文件应为只读");
        
        // 使用FileTransferTestUtils清理目录
        FileTransferTestUtils.cleanupTestDirectory(testDir.getAbsolutePath());
        
        // 验证子文件已被删除，但目录仍存在
        assertFalse(subFile.exists(), "子文件应已被删除");
        assertTrue(testDir.exists(), "目录应仍存在");
    }
    
    @Test
    @DisplayName("测试FileTransferTestUtils删除文件")
    void testFileTransferTestUtilsDeleteFile() throws IOException {
        // 设置文件为只读
        FilePermissionUtils.setReadOnly(testFile);
        assertFalse(testFile.canWrite(), "文件应为只读");
        
        // 使用FileTransferTestUtils删除文件
        boolean deleted = FileTransferTestUtils.deleteTestFile(testFile.getAbsolutePath());
        assertTrue(deleted, "删除只读文件应成功");
        assertFalse(testFile.exists(), "文件应已被删除");
    }
    
    @Test
    @DisplayName("测试混合权限场景")
    void testMixedPermissionScenario() throws IOException {
        // 创建复杂的目录结构
        File level1Dir = new File(testDir, "level1");
        level1Dir.mkdirs();
        
        File level2Dir = new File(level1Dir, "level2");
        level2Dir.mkdirs();
        
        File file1 = new File(level1Dir, "file1.txt");
        File file2 = new File(level2Dir, "file2.txt");
        Files.write(file1.toPath(), "文件1内容".getBytes());
        Files.write(file2.toPath(), "文件2内容".getBytes());
        
        // 设置不同的权限
        FilePermissionUtils.setReadOnly(file1);  // 文件1只读
        FilePermissionUtils.setDirectoryOnlyPermissions(level1Dir, true);  // level1目录只读
        // file2和level2Dir保持可写
        
        // 验证权限设置
        assertFalse(file1.canWrite(), "文件1应为只读");
        assertFalse(level1Dir.canWrite(), "level1目录应为只读");
        assertTrue(file2.canWrite(), "文件2应为可写");
        assertTrue(level2Dir.canWrite(), "level2目录应为可写");
        
        // 使用递归删除清理整个结构
        boolean deleted = FileTransferTestUtils.deleteDirectoryRecursively(testDir);
        assertTrue(deleted, "递归删除混合权限目录应成功");
        assertFalse(testDir.exists(), "整个目录结构应已被删除");
    }
}
